package config

import (
	"fmt"
	"log"
	"os"

	"gopkg.in/yaml.v3"
)

// Config 应用程序配置
type Config struct {
	Database DatabaseConfig `yaml:"database"`
	Server   ServerConfig   `yaml:"server"`
	Logging  LoggingConfig  `yaml:"logging"`
	Security SecurityConfig `yaml:"security"`
	BaseNet  BaseNetConfig  `yaml:"basenet"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type      string          `yaml:"type"`
	SQLite    SQLiteConfig    `yaml:"sqlite"`
	MongoDB   MongoDBConfig   `yaml:"mongodb"`
	SQLServer SQLServerConfig `yaml:"sqlserver"`
}

// SQLiteConfig SQLite配置
type SQLiteConfig struct {
	Path string `yaml:"path"`
}

// MongoDBConfig MongoDB配置
type MongoDBConfig struct {
	URI        string `yaml:"uri"`
	Database   string `yaml:"database"`
	Collection string `yaml:"collection"`
}

// SQLServerConfig SQL Server配置
type SQLServerConfig struct {
	Server   string `yaml:"server"`   // 服务器地址
	Port     int    `yaml:"port"`     // 端口号，默认1433
	Database string `yaml:"database"` // 数据库名
	Username string `yaml:"username"` // 用户名
	Password string `yaml:"password"` // 密码
	Instance string `yaml:"instance"` // 实例名（可选）
	Encrypt  bool   `yaml:"encrypt"`  // 是否启用加密，默认true
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port int        `yaml:"port"`
	Host string     `yaml:"host"`
	CORS CORSConfig `yaml:"cors"`
}

// CORSConfig CORS配置
type CORSConfig struct {
	Enabled        bool     `yaml:"enabled"`
	AllowedOrigins []string `yaml:"allowed_origins"`
	AllowedMethods []string `yaml:"allowed_methods"`
	AllowedHeaders []string `yaml:"allowed_headers"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
	Output string `yaml:"output"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	AppSecret string `yaml:"app_secret"`
}

// BaseNetConfig BaseNet API配置
type BaseNetConfig struct {
	Platforms  PlatformsConfig `yaml:"platforms"`
	GameConfig GameConfig      `yaml:"game_config"`
	AdsConfig  AdsConfig       `yaml:"ads_config"`
}

// PlatformsConfig 平台配置
type PlatformsConfig struct {
	Wechat PlatformConfig `yaml:"wechat"`
	Baidu  PlatformConfig `yaml:"baidu"`
	QQ     PlatformConfig `yaml:"qq"`
}

// PlatformConfig 单个平台配置
type PlatformConfig struct {
	AppID     string `yaml:"app_id"`
	AppSecret string `yaml:"app_secret"`
}

// GameConfig 游戏配置
type GameConfig struct {
	MaxLevel    int  `yaml:"max_level"`
	DailyReward bool `yaml:"daily_reward"`
	Maintenance bool `yaml:"maintenance"`
	ForceUpdate bool `yaml:"force_update"`
}

// AdsConfig 广告配置
type AdsConfig struct {
	Banner       AdTypeConfig `yaml:"banner"`
	Video        AdTypeConfig `yaml:"video"`
	Interstitial AdTypeConfig `yaml:"interstitial"`
}

// AdTypeConfig 广告类型配置
type AdTypeConfig struct {
	Enabled         bool `yaml:"enabled"`
	RefreshInterval int  `yaml:"refresh_interval,omitempty"`
	RewardAmount    int  `yaml:"reward_amount,omitempty"`
	Frequency       int  `yaml:"frequency,omitempty"`
}

// LoadConfig 从YAML文件加载配置
func LoadConfig(configPath string) (*Config, error) {
	// 如果没有指定配置文件路径，使用默认路径
	if configPath == "" {
		configPath = "config.yaml"
	}

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析YAML
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	// 打印配置信息
	logConfigInfo(&config)

	return &config, nil
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	// 验证数据库类型
	if config.Database.Type != "sqlite" && config.Database.Type != "mongodb" {
		return fmt.Errorf("不支持的数据库类型: %s", config.Database.Type)
	}

	// 验证SQLite配置
	if config.Database.Type == "sqlite" && config.Database.SQLite.Path == "" {
		return fmt.Errorf("SQLite路径不能为空")
	}

	// 验证MongoDB配置
	if config.Database.Type == "mongodb" {
		if config.Database.MongoDB.URI == "" {
			return fmt.Errorf("MongoDB URI不能为空")
		}
		if config.Database.MongoDB.Database == "" {
			return fmt.Errorf("MongoDB数据库名不能为空")
		}
		if config.Database.MongoDB.Collection == "" {
			return fmt.Errorf("MongoDB集合名不能为空")
		}
	}

	// 验证服务器端口
	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		return fmt.Errorf("无效的服务器端口: %d", config.Server.Port)
	}

	return nil
}

// logConfigInfo 打印配置信息
func logConfigInfo(config *Config) {
	log.Printf("数据库配置: Type=%s", config.Database.Type)
	if config.Database.Type == "mongodb" {
		log.Printf("MongoDB URI: %s, DB: %s, Collection: %s",
			config.Database.MongoDB.URI,
			config.Database.MongoDB.Database,
			config.Database.MongoDB.Collection)
	} else {
		log.Printf("SQLite Path: %s", config.Database.SQLite.Path)
	}
	log.Printf("服务器配置: Host=%s, Port=%d", config.Server.Host, config.Server.Port)
}

// GetDatabaseConfig 获取数据库配置（兼容旧接口）
func (c *Config) GetDatabaseConfig() *DatabaseConfig {
	return &c.Database
}

// GetSQLitePath 获取SQLite路径
func (c *Config) GetSQLitePath() string {
	return c.Database.SQLite.Path
}

// GetMongoURI 获取MongoDB URI
func (c *Config) GetMongoURI() string {
	return c.Database.MongoDB.URI
}

// GetMongoDB 获取MongoDB数据库名
func (c *Config) GetMongoDB() string {
	return c.Database.MongoDB.Database
}

// GetMongoCollection 获取MongoDB集合名
func (c *Config) GetMongoCollection() string {
	return c.Database.MongoDB.Collection
}
