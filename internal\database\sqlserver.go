package database

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"iaa-gamelog/internal/models"

	_ "github.com/microsoft/go-mssqldb"
)

// SQLServerDatabase SQL Server数据库操作结构体
type SQLServerDatabase struct {
	db *sql.DB
}

// NewSQLServerDatabase 创建新的SQL Server数据库连接
func NewSQLServerDatabase(server string, port int, database, username, password, instance string, encrypt bool) (*SQLServerDatabase, error) {
	// 构建连接字符串
	var connString string
	if instance != "" {
		connString = fmt.Sprintf("server=%s\\%s;port=%d;database=%s;user id=%s;password=%s;encrypt=%s",
			server, instance, port, database, username, password, boolToString(encrypt))
	} else {
		connString = fmt.Sprintf("server=%s;port=%d;database=%s;user id=%s;password=%s;encrypt=%s",
			server, port, database, username, password, boolToString(encrypt))
	}

	// 连接数据库
	db, err := sql.Open("sqlserver", connString)
	if err != nil {
		return nil, fmt.Errorf("连接SQL Server失败: %v", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(30 * time.Minute)
	db.SetConnMaxIdleTime(5 * time.Minute)

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("SQL Server连接测试失败: %v", err)
	}

	// 创建表
	if err := createTables(db); err != nil {
		return nil, fmt.Errorf("创建表失败: %v", err)
	}

	log.Println("SQL Server数据库初始化成功")

	return &SQLServerDatabase{db: db}, nil
}

// boolToString 将布尔值转换为字符串
func boolToString(b bool) string {
	if b {
		return "true"
	}
	return "false"
}

// createTables 创建数据库表
func createTables(db *sql.DB) error {
	// 创建游戏记录表
	createGameRecordsTableSQL := `
	IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='game_records' AND xtype='U')
	CREATE TABLE game_records (
		id BIGINT IDENTITY(1,1) PRIMARY KEY,
		user_id NVARCHAR(255) NOT NULL,
		game_name NVARCHAR(255) NOT NULL,
		game_data NVARCHAR(MAX) NOT NULL,
		created_at DATETIME2 DEFAULT GETDATE(),
		updated_at DATETIME2 DEFAULT GETDATE(),
		CONSTRAINT UK_game_records_user_game UNIQUE (user_id, game_name)
	);`

	_, err := db.Exec(createGameRecordsTableSQL)
	if err != nil {
		return fmt.Errorf("创建游戏记录表失败: %v", err)
	}

	// 创建白名单表
	createWhitelistTableSQL := `
	IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='whitelist' AND xtype='U')
	CREATE TABLE whitelist (
		id BIGINT IDENTITY(1,1) PRIMARY KEY,
		app_name NVARCHAR(255) NOT NULL,
		uuid NVARCHAR(255) NOT NULL,
		created_at DATETIME2 DEFAULT GETDATE(),
		updated_at DATETIME2 DEFAULT GETDATE(),
		CONSTRAINT UK_whitelist_app_uuid UNIQUE (app_name, uuid)
	);`

	_, err = db.Exec(createWhitelistTableSQL)
	if err != nil {
		return fmt.Errorf("创建白名单表失败: %v", err)
	}

	// 创建索引
	createIndexes := []string{
		"CREATE NONCLUSTERED INDEX IX_game_records_user_id ON game_records (user_id);",
		"CREATE NONCLUSTERED INDEX IX_game_records_game_name ON game_records (game_name);",
		"CREATE NONCLUSTERED INDEX IX_game_records_updated_at ON game_records (updated_at DESC);",
		"CREATE NONCLUSTERED INDEX IX_whitelist_app_name ON whitelist (app_name);",
		"CREATE NONCLUSTERED INDEX IX_whitelist_uuid ON whitelist (uuid);",
		"CREATE NONCLUSTERED INDEX IX_whitelist_created_at ON whitelist (created_at DESC);",
	}

	for _, indexSQL := range createIndexes {
		// 检查索引是否已存在，如果不存在则创建
		indexName := extractIndexName(indexSQL)
		checkSQL := fmt.Sprintf("SELECT COUNT(*) FROM sys.indexes WHERE name = '%s'", indexName)
		var count int
		err := db.QueryRow(checkSQL).Scan(&count)
		if err == nil && count == 0 {
			_, err = db.Exec(indexSQL)
			if err != nil {
				log.Printf("创建索引失败: %v", err)
			}
		}
	}

	log.Println("SQL Server表和索引创建成功")
	return nil
}

// extractIndexName 从CREATE INDEX语句中提取索引名
func extractIndexName(sql string) string {
	parts := strings.Fields(sql)
	for i, part := range parts {
		if strings.ToUpper(part) == "INDEX" && i+1 < len(parts) {
			return parts[i+1]
		}
	}
	return ""
}

// Close 关闭SQL Server数据库连接
func (s *SQLServerDatabase) Close() error {
	return s.db.Close()
}

// SaveRecord 保存游戏记录（不存在时新插入，存在时更新）
func (s *SQLServerDatabase) SaveRecord(userID, gameName string, gameData interface{}) (interface{}, string, error) {
	// 将游戏数据转换为JSON字符串
	gameDataJSON, err := json.Marshal(gameData)
	if err != nil {
		return nil, "", err
	}

	var action string
	var recordId int64

	// 开始事务
	tx, err := s.db.Begin()
	if err != nil {
		return nil, "", err
	}
	defer tx.Rollback()

	now := time.Now()

	// 使用MERGE语句实现UPSERT操作
	mergeSQL := `
	MERGE game_records AS target
	USING (SELECT ? AS user_id, ? AS game_name, ? AS game_data, ? AS updated_at) AS source
	ON (target.user_id = source.user_id AND target.game_name = source.game_name)
	WHEN MATCHED THEN
		UPDATE SET game_data = source.game_data, updated_at = source.updated_at
	WHEN NOT MATCHED THEN
		INSERT (user_id, game_name, game_data, created_at, updated_at)
		VALUES (source.user_id, source.game_name, source.game_data, ?, source.updated_at)
	OUTPUT $action, INSERTED.id;`

	var actionResult string
	err = tx.QueryRow(mergeSQL, userID, gameName, string(gameDataJSON), now, now).Scan(&actionResult, &recordId)
	if err != nil {
		return nil, "", err
	}

	// 根据MERGE操作结果确定动作类型
	if actionResult == "INSERT" {
		action = "created"
	} else {
		action = "updated"
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return nil, "", err
	}

	return recordId, action, nil
}

// GetRecords 获取游戏记录
func (s *SQLServerDatabase) GetRecords(userID, gameName string, limit, offset int) ([]models.GameRecord, int64, error) {
	// 构建查询条件
	var whereConditions []string
	var args []interface{}
	argIndex := 1

	if userID != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("user_id = @p%d", argIndex))
		args = append(args, userID)
		argIndex++
	}
	if gameName != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("game_name = @p%d", argIndex))
		args = append(args, gameName)
		argIndex++
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// 获取总数
	countSQL := fmt.Sprintf("SELECT COUNT(*) FROM game_records %s", whereClause)
	var total int64
	err := s.db.QueryRow(countSQL, args...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// 查询记录
	querySQL := fmt.Sprintf(`
		SELECT id, user_id, game_name, game_data, created_at, updated_at
		FROM game_records %s
		ORDER BY updated_at DESC
		OFFSET @p%d ROWS FETCH NEXT @p%d ROWS ONLY`,
		whereClause, argIndex, argIndex+1)

	args = append(args, offset, limit)

	rows, err := s.db.Query(querySQL, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var records []models.GameRecord
	for rows.Next() {
		var record models.GameRecord
		err := rows.Scan(&record.ID, &record.UserID, &record.GameName, &record.GameData, &record.CreatedAt, &record.UpdatedAt)
		if err != nil {
			return nil, 0, err
		}
		records = append(records, record)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// DeleteRecord 删除游戏记录
func (s *SQLServerDatabase) DeleteRecord(userID, gameName string) error {
	deleteSQL := "DELETE FROM game_records WHERE user_id = ? AND game_name = ?"
	result, err := s.db.Exec(deleteSQL, userID, gameName)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return fmt.Errorf("记录不存在")
	}

	return nil
}
